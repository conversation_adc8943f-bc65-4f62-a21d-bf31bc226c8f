// Simple direct server that doesn't rely on complex drizzle-orm features
// This script avoids the entity.ts error by using a simplified approach

// Import required modules
import express from 'express';
import postgres from 'postgres';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Set environment variables
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
process.env.PGSSLMODE = 'no-verify';
process.env.PG_SSL_REJECT_UNAUTHORIZED = '0';
process.env.VERCEL = 'true';
process.env.PORT = '3000';
process.env.HOST = '0.0.0.0'; // Bind to all interfaces

// Load environment variables from .env.vercel
const envPath = path.join(__dirname, '.env.vercel');
if (fs.existsSync(envPath)) {
  console.log('Loading environment variables from .env.vercel');
  dotenv.config({ path: envPath });
} else {
  console.log('No .env.vercel file found, using default environment variables');
  dotenv.config();
}

console.log('Starting server with environment:');
console.log({
  NODE_TLS_REJECT_UNAUTHORIZED: process.env.NODE_TLS_REJECT_UNAUTHORIZED,
  PGSSLMODE: process.env.PGSSLMODE,
  VERCEL: process.env.VERCEL,
  PORT: process.env.PORT,
  HOST: process.env.HOST,
  DATABASE_URL: process.env.DATABASE_URL ? '✓ Present' : '✗ Missing',
  POSTGRES_URL: process.env.POSTGRES_URL ? '✓ Present' : '✗ Missing'
});

// Get connection string from environment variables
const connectionString = process.env.POSTGRES_URL || process.env.DATABASE_URL;

if (!connectionString) {
  throw new Error("Neither POSTGRES_URL nor DATABASE_URL is defined in environment variables");
}

// Create a direct postgres client without using drizzle-orm
const sql = postgres(connectionString, {
  ssl: { rejectUnauthorized: false },
  max: 3,
  idle_timeout: 20,
  connect_timeout: 10,
  prepare: false
});

// Server-side cache to reduce database queries
const serverCache = {
  categories: { data: null, timestamp: 0, ttl: 15 * 60 * 1000 }, // 15 minutes
  products: { data: null, timestamp: 0, ttl: 10 * 60 * 1000 }, // 10 minutes
  users: new Map(), // User-specific cache with TTL
};

// Cache helper functions
function isCacheValid(cacheEntry) {
  return cacheEntry.data && (Date.now() - cacheEntry.timestamp) < cacheEntry.ttl;
}

function setCacheData(key, data, customTtl = null) {
  if (serverCache[key]) {
    serverCache[key].data = data;
    serverCache[key].timestamp = Date.now();
    if (customTtl) serverCache[key].ttl = customTtl;
  }
}

function getCacheData(key) {
  if (serverCache[key] && isCacheValid(serverCache[key])) {
    return serverCache[key].data;
  }
  return null;
}

// User-specific cache functions
function getUserCacheKey(userId, type) {
  return `${userId}:${type}`;
}

function setUserCache(userId, type, data, ttl = 5 * 60 * 1000) { // 5 minutes default
  const key = getUserCacheKey(userId, type);
  serverCache.users.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  });
}

function getUserCache(userId, type) {
  const key = getUserCacheKey(userId, type);
  const cached = serverCache.users.get(key);
  if (cached && (Date.now() - cached.timestamp) < cached.ttl) {
    return cached.data;
  }
  return null;
}

// Clear expired cache entries periodically
setInterval(() => {
  const now = Date.now();
  // Clear expired user cache entries
  for (const [key, entry] of serverCache.users.entries()) {
    if ((now - entry.timestamp) > entry.ttl) {
      serverCache.users.delete(key);
    }
  }
}, 5 * 60 * 1000); // Clean up every 5 minutes

// Create Express app
const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Add CORS headers for development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  next();
});

// Set up static file serving - check multiple possible locations
const possiblePublicDirs = [
  path.join(process.cwd(), 'client/dist'),
  path.join(process.cwd(), 'dist/public'),
  path.join(process.cwd(), 'dist'),
  path.join(process.cwd(), 'public'),
  path.join(process.cwd(), 'client/public'),
  path.join(__dirname, 'dist/public'),
  path.join(__dirname, 'dist'),
  path.join(__dirname, 'public')
];

// Check if client directory exists and add it to possible directories
const clientDir = path.join(process.cwd(), 'client');
if (fs.existsSync(clientDir)) {
  console.log(`Found client directory at ${clientDir}`);
  // Also check for client/dist directory
  const clientDistDir = path.join(clientDir, 'dist');
  if (fs.existsSync(clientDistDir)) {
    console.log(`Found client/dist directory at ${clientDistDir}`);
  }
}

let publicDir = '';
for (const dir of possiblePublicDirs) {
  if (fs.existsSync(dir)) {
    publicDir = dir;
    console.log(`Found public directory at ${dir}`);
    break;
  }
}

if (!publicDir) {
  publicDir = path.join(process.cwd(), 'public');
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
    console.log(`Created public directory at ${publicDir}`);
  }
}

// Serve static files from the public directory with proper caching headers
app.use(express.static(publicDir, {
  etag: true,
  lastModified: true,
  setHeaders: (res, path) => {
    // Set caching headers for images
    if (path.endsWith('.jpg') || path.endsWith('.jpeg') || path.endsWith('.png') || path.endsWith('.gif')) {
      res.setHeader('Cache-Control', 'public, max-age=86400'); // 1 day
    } else if (path.endsWith('.css') || path.endsWith('.js')) {
      res.setHeader('Cache-Control', 'public, max-age=3600'); // 1 hour
    } else {
      res.setHeader('Cache-Control', 'public, max-age=60'); // 1 minute
    }
  }
}));
console.log(`Serving static files from ${publicDir}`);

// Also serve the dist directory if it exists
const distDir = path.join(process.cwd(), 'dist');
if (fs.existsSync(distDir)) {
  app.use(express.static(distDir, {
    etag: true,
    lastModified: true,
    setHeaders: (res, path) => {
      // Set caching headers for images
      if (path.endsWith('.jpg') || path.endsWith('.jpeg') || path.endsWith('.png') || path.endsWith('.gif')) {
        res.setHeader('Cache-Control', 'public, max-age=86400'); // 1 day
      } else if (path.endsWith('.css') || path.endsWith('.js')) {
        res.setHeader('Cache-Control', 'public, max-age=3600'); // 1 hour
      } else {
        res.setHeader('Cache-Control', 'public, max-age=60'); // 1 minute
      }
    }
  }));
  console.log(`Also serving static files from ${distDir}`);
}

// Also serve the client/public directory if it exists
const clientPublicDir = path.join(process.cwd(), 'client/public');
if (fs.existsSync(clientPublicDir)) {
  app.use(express.static(clientPublicDir, {
    etag: true,
    lastModified: true
  }));
  console.log(`Also serving static files from ${clientPublicDir}`);
}

// Also serve the client/dist directory if it exists
const clientDistDir = path.join(process.cwd(), 'client/dist');
if (fs.existsSync(clientDistDir)) {
  app.use(express.static(clientDistDir, {
    etag: true,
    lastModified: true
  }));
  console.log(`Also serving static files from ${clientDistDir}`);
}

// Check if we have an index.html file
const indexHtmlPaths = [
  path.join(publicDir, 'index.html'),
  path.join(distDir, 'index.html'),
  path.join(process.cwd(), 'dist/public/index.html'),
  path.join(process.cwd(), 'index.html')
];

let indexHtmlPath = '';
for (const htmlPath of indexHtmlPaths) {
  if (fs.existsSync(htmlPath)) {
    indexHtmlPath = htmlPath;
    console.log(`Found index.html at ${htmlPath}`);
    break;
  }
}

// Add a simple test endpoint
app.get('/test', (req, res) => {
  res.send('Server is running correctly! You can access this endpoint.');
});

// Add a simple HTML test endpoint
app.get('/test-html', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>Test Page</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          h1 { color: #333; }
          .info { background: #f5f5f5; padding: 15px; border-radius: 5px; }
        </style>
      </head>
      <body>
        <h1>Server is running correctly!</h1>
        <div class="info">
          <p>This is a test HTML page served by the simplified direct server.</p>
          <p>Environment: ${process.env.NODE_ENV || 'development'}</p>
          <p>Database: ${process.env.DATABASE_URL ? 'Configured' : 'Not configured'}</p>
        </div>
      </body>
    </html>
  `);
});

// Add a simple API endpoint to test database connection
app.get('/api/health', async (req, res) => {
  try {
    // Test database connection
    const result = await sql`SELECT 1 as connected`;
    res.json({
      status: 'ok',
      database: 'connected',
      environment: process.env.NODE_ENV || 'development',
      vercel: process.env.VERCEL ? true : false
    });
  } catch (error) {
    console.error('Database connection error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Database connection failed',
      error: error.message
    });
  }
});

// Add a debug endpoint to help diagnose issues
app.get('/api/debug-info', (req, res) => {
  // Collect environment information
  const environment = {
    NODE_ENV: process.env.NODE_ENV || 'development',
    VERCEL: process.env.VERCEL ? true : false,
    VERCEL_ENV: process.env.VERCEL_ENV,
    VERCEL_URL: process.env.VERCEL_URL,
    VERCEL_REGION: process.env.VERCEL_REGION
  };

  // Collect database configuration
  const database = {
    DATABASE_URL: process.env.DATABASE_URL ? 'configured' : 'missing',
    POSTGRES_URL: process.env.POSTGRES_URL ? 'configured' : 'missing',
    POSTGRES_HOST: process.env.POSTGRES_HOST || 'not configured',
    POSTGRES_USER: process.env.POSTGRES_USER ? 'configured' : 'missing',
    POSTGRES_DATABASE: process.env.POSTGRES_DATABASE || process.env.PGDATABASE || 'not configured'
  };

  // Collect SSL configuration
  const ssl = {
    NODE_TLS_REJECT_UNAUTHORIZED: process.env.NODE_TLS_REJECT_UNAUTHORIZED,
    PGSSLMODE: process.env.PGSSLMODE,
    PG_SSL_REJECT_UNAUTHORIZED: process.env.PG_SSL_REJECT_UNAUTHORIZED,
    DISABLE_SSL_VALIDATION: process.env.DISABLE_SSL_VALIDATION
  };

  // Collect request information
  const request = {
    method: req.method,
    url: req.url,
    headers: req.headers,
    host: req.headers.host,
    origin: req.headers.origin,
    referer: req.headers.referer
  };

  // Return all collected information
  res.status(200).json({
    timestamp: new Date().toISOString(),
    environment,
    database,
    ssl,
    request,
    message: 'This endpoint provides debug information for troubleshooting deployment issues'
  });
});

// Add a simple API endpoint to get categories
app.get('/api/categories', async (req, res) => {
  try {
    console.log('Fetching categories');

    // Check cache first
    let categories = getCacheData('categories');
    if (categories) {
      console.log('Returning cached categories');
      return res.json(categories);
    }

    // Try to get categories from database
    try {
      categories = await sql`SELECT * FROM categories`;
      console.log(`Found ${categories.length} categories in database`);
    } catch (dbError) {
      console.error('Database error fetching categories:', dbError);
      // If database query fails, use fallback categories
      categories = [];
    }

    // If no categories found in database, use fallback categories
    if (!categories || categories.length === 0) {
      console.log('Using fallback categories');
      categories = [
        {
          id: 629,
          name: "Recycled Electronics",
          imageUrl: "https://images.pexels.com/photos/3850512/pexels-photo-3850512.jpeg"
        },
        {
          id: 630,
          name: "Upcycled Fashion",
          imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg"
        },
        {
          id: 631,
          name: "Eco-friendly Home",
          imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg"
        },
        {
          id: 632,
          name: "Recycled Accessories",
          imageUrl: "https://images.pexels.com/photos/7270290/pexels-photo-7270290.jpeg"
        }
      ];
    }

    // Try to insert the fallback categories into the database if they don't exist
    try {
      console.log('Checking if we need to insert fallback categories');
      const existingCategories = await sql`SELECT id FROM categories`;

      if (existingCategories.length === 0) {
        console.log('Inserting fallback categories into database');
        for (const category of categories) {
          await sql`
            INSERT INTO categories (id, name, image_url)
            VALUES (${category.id}, ${category.name}, ${category.imageUrl})
            ON CONFLICT (id) DO NOTHING
          `;
        }
        console.log('Fallback categories inserted successfully');
      }
    } catch (dbError) {
      console.error('Error inserting fallback categories:', dbError);
    }

    // Convert snake_case to camelCase for response
    const formattedCategories = categories.map(category => {
      // If it's already in camelCase format, return as is
      if (category.imageUrl) {
        return category;
      }

      // Otherwise convert from snake_case
      return {
        id: category.id,
        name: category.name,
        imageUrl: category.image_url
      };
    });

    console.log(`Returning ${formattedCategories.length} formatted categories`);

    // Cache the formatted categories
    setCacheData('categories', formattedCategories);

    res.json(formattedCategories);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch categories',
      error: error.message
    });
  }
});

// Add a simple API endpoint to get products with filtering
app.get('/api/products', async (req, res) => {
  try {
    console.log('Fetching products with query params:', req.query);

    // Extract filter parameters
    const {
      category,
      search,
      minPrice,
      maxPrice,
      isNew,
      isPopular,
      isSale,
      sort
    } = req.query;

    // Create cache key based on query parameters
    const cacheKey = `products:${JSON.stringify(req.query)}`;

    // Check if we have cached data for this specific query
    let cachedProducts = getCacheData('products');
    if (cachedProducts && Object.keys(req.query).length === 0) {
      // Only use cache for basic product requests without filters
      console.log('Returning cached products');
      return res.json(cachedProducts);
    }

    // Define fallback products
    const fallbackProducts = [
      {
        id: 741,
        name: "Refurbished Smartphone",
        description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
        price: 329.99,
        discount_price: 299.99,
        rating: 4.5,
        review_count: 34,
        image_url: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg",
        category_id: 629,
        in_stock: true,
        is_new: true,
        is_popular: false,
        is_sale: true
      },
      {
        id: 742,
        name: "Upcycled Denim Jacket",
        description: "Handcrafted jacket made from reclaimed denim. Each piece is unique and helps reduce textile waste.",
        price: 89.99,
        discount_price: null,
        rating: 4.8,
        review_count: 42,
        image_url: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg",
        category_id: 630,
        in_stock: true,
        is_new: true,
        is_popular: true,
        is_sale: false
      },
      {
        id: 743,
        name: "Recycled Metal Water Bottle",
        description: "Vacuum-sealed water bottle made from recycled metals. Keeps drinks cold for 24 hours or hot for 12 hours.",
        price: 29.99,
        discount_price: 24.99,
        rating: 4.9,
        review_count: 120,
        image_url: "https://images.pexels.com/photos/3737903/pexels-photo-3737903.jpeg",
        category_id: 632,
        in_stock: true,
        is_new: false,
        is_popular: true,
        is_sale: true
      },
      {
        id: 744,
        name: "Bamboo Desk Organizer",
        description: "Sustainable bamboo desk organizer with multiple compartments for all your office essentials.",
        price: 49.99,
        discount_price: null,
        rating: 4.6,
        review_count: 28,
        image_url: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg",
        category_id: 631,
        in_stock: true,
        is_new: false,
        is_popular: false,
        is_sale: false
      }
    ];

    // Check if we need to create sample products
    try {
      const productCount = await sql`SELECT COUNT(*) FROM products`;

      if (productCount[0].count === '0') {
        console.log('No products found, creating sample products');

        // Create sample products
        for (const product of fallbackProducts) {
          await sql`
            INSERT INTO products (
              id, name, description, price, discount_price, rating, review_count,
              image_url, category_id, in_stock, is_new, is_popular, is_sale
            ) VALUES (
              ${product.id}, ${product.name}, ${product.description}, ${product.price},
              ${product.discount_price}, ${product.rating}, ${product.review_count},
              ${product.image_url}, ${product.category_id}, ${product.in_stock},
              ${product.is_new}, ${product.is_popular}, ${product.is_sale}
            ) ON CONFLICT (id) DO NOTHING
          `;
        }

        console.log('Sample products created successfully');
      }
    } catch (dbError) {
      console.error('Error checking or creating sample products:', dbError);
    }

    // Build the query based on filters
    let query = `SELECT * FROM products WHERE 1=1`;
    const params = [];

    // Add category filter
    if (category) {
      try {
        const categoryId = parseInt(category);
        console.log(`Filtering by category ID: ${categoryId}`);

        query += ` AND category_id = $${params.length + 1}`;
        params.push(categoryId);
      } catch (error) {
        console.error(`Error parsing category ID ${category}:`, error);
      }
    }

    // Add search filter
    if (search) {
      query += ` AND (name ILIKE $${params.length + 1} OR description ILIKE $${params.length + 1})`;
      params.push(`%${search}%`);
    }

    // Add price range filter
    if (minPrice) {
      query += ` AND price >= $${params.length + 1}`;
      params.push(parseFloat(minPrice));
    }

    if (maxPrice) {
      query += ` AND price <= $${params.length + 1}`;
      params.push(parseFloat(maxPrice));
    }

    // Add boolean filters
    if (isNew === 'true') {
      query += ` AND is_new = true`;
    }

    if (isPopular === 'true') {
      query += ` AND is_popular = true`;
    }

    if (isSale === 'true') {
      query += ` AND is_sale = true`;
    }

    // Add sorting
    if (sort) {
      switch (sort) {
        case 'price-asc':
          query += ` ORDER BY price ASC`;
          break;
        case 'price-desc':
          query += ` ORDER BY price DESC`;
          break;
        case 'rating':
          query += ` ORDER BY rating DESC`;
          break;
        default:
          query += ` ORDER BY id DESC`; // newest by default
      }
    } else {
      query += ` ORDER BY id DESC`; // newest by default
    }

    console.log('Executing query:', query, 'with params:', params);

    // Execute the query
    let products = [];
    try {
      products = await sql.unsafe(query, params);
      console.log(`Found ${products.length} products matching the criteria`);
    } catch (queryError) {
      console.error('Error executing product query:', queryError);
      console.log('Using fallback products instead');

      // Filter fallback products based on query parameters
      products = fallbackProducts.filter(product => {
        // Apply category filter
        if (category) {
          try {
            const categoryId = parseInt(category);
            if (product.category_id !== categoryId) {
              return false;
            }
          } catch (error) {
            console.error(`Error parsing category ID ${category} for fallback filtering:`, error);
          }
        }

        // Apply search filter
        if (search && !(
          product.name.toLowerCase().includes(search.toLowerCase()) ||
          product.description.toLowerCase().includes(search.toLowerCase())
        )) {
          return false;
        }

        // Apply price filters
        if (minPrice && product.price < parseFloat(minPrice)) {
          return false;
        }
        if (maxPrice && product.price > parseFloat(maxPrice)) {
          return false;
        }

        // Apply boolean filters
        if (isNew === 'true' && !product.is_new) {
          return false;
        }
        if (isPopular === 'true' && !product.is_popular) {
          return false;
        }
        if (isSale === 'true' && !product.is_sale) {
          return false;
        }

        return true;
      });

      console.log(`Using ${products.length} fallback products after filtering`);
    }

    // Convert snake_case to camelCase for response
    const formattedProducts = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      discountPrice: product.discount_price,
      rating: product.rating,
      reviewCount: product.review_count,
      imageUrl: product.image_url,
      categoryId: product.category_id,
      inStock: product.in_stock,
      isNew: product.is_new,
      isPopular: product.is_popular,
      isSale: product.is_sale,
      createdAt: product.created_at
    }));

    console.log(`Returning ${formattedProducts.length} formatted products`);

    // Cache products if no filters applied (basic request)
    if (Object.keys(req.query).length === 0) {
      setCacheData('products', formattedProducts);
    }

    res.json(formattedProducts);
  } catch (error) {
    console.error('Error fetching products:', error);

    // Return fallback products instead of an error
    console.log('Returning fallback products due to error');
    const fallbackProducts = [
      {
        id: 741,
        name: "Refurbished Smartphone",
        description: "Like-new refurbished phone with 1-year warranty. Eco-friendly choice that reduces e-waste.",
        price: 329.99,
        discountPrice: 299.99,
        rating: 4.5,
        reviewCount: 34,
        imageUrl: "https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg",
        categoryId: 629,
        inStock: true,
        isNew: true,
        isPopular: false,
        isSale: true
      },
      {
        id: 742,
        name: "Upcycled Denim Jacket",
        description: "Handcrafted jacket made from reclaimed denim. Each piece is unique and helps reduce textile waste.",
        price: 89.99,
        discountPrice: null,
        rating: 4.8,
        reviewCount: 42,
        imageUrl: "https://images.pexels.com/photos/6593354/pexels-photo-6593354.jpeg",
        categoryId: 630,
        inStock: true,
        isNew: true,
        isPopular: true,
        isSale: false
      },
      {
        id: 743,
        name: "Recycled Metal Water Bottle",
        description: "Vacuum-sealed water bottle made from recycled metals. Keeps drinks cold for 24 hours or hot for 12 hours.",
        price: 29.99,
        discountPrice: 24.99,
        rating: 4.9,
        reviewCount: 120,
        imageUrl: "https://images.pexels.com/photos/3737903/pexels-photo-3737903.jpeg",
        categoryId: 632,
        inStock: true,
        isNew: false,
        isPopular: true,
        isSale: true
      },
      {
        id: 744,
        name: "Bamboo Desk Organizer",
        description: "Sustainable bamboo desk organizer with multiple compartments for all your office essentials.",
        price: 49.99,
        discountPrice: null,
        rating: 4.6,
        reviewCount: 28,
        imageUrl: "https://images.pexels.com/photos/4946978/pexels-photo-4946978.jpeg",
        categoryId: 631,
        inStock: true,
        isNew: false,
        isPopular: false,
        isSale: false
      }
    ];

    res.json(fallbackProducts);
  }
});

// Add a simple API endpoint to get a product by ID
app.get('/api/products/:id', async (req, res) => {
  try {
    // Validate ID parameter
    const idParam = req.params.id;
    console.log(`Fetching product with ID: ${idParam}`);

    // Special handling for featured, new, and sale products
    if (idParam === 'featured') {
      console.log('Fetching featured products');
      const featuredProducts = await sql`SELECT * FROM products WHERE is_popular = true LIMIT 8`;

      // Format the products to ensure consistent structure
      const formattedProducts = featuredProducts.map(product => ({
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        discountPrice: product.discount_price,
        rating: product.rating,
        reviewCount: product.review_count,
        imageUrl: product.image_url,
        categoryId: product.category_id,
        inStock: product.in_stock,
        isNew: product.is_new,
        isPopular: product.is_popular,
        isSale: product.is_sale,
        createdAt: product.created_at
      }));

      console.log(`Returning ${formattedProducts.length} formatted popular products`);
      return res.json(formattedProducts);
    }

    if (idParam === 'new') {
      console.log('Fetching new products');
      const newProducts = await sql`SELECT * FROM products WHERE is_new = true LIMIT 8`;

      // Format the products to ensure consistent structure
      const formattedProducts = newProducts.map(product => ({
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        discountPrice: product.discount_price,
        rating: product.rating,
        reviewCount: product.review_count,
        imageUrl: product.image_url,
        categoryId: product.category_id,
        inStock: product.in_stock,
        isNew: product.is_new,
        isPopular: product.is_popular,
        isSale: product.is_sale,
        createdAt: product.created_at
      }));

      console.log(`Returning ${formattedProducts.length} formatted new products`);
      return res.json(formattedProducts);
    }

    if (idParam === 'sale') {
      console.log('Fetching sale products');
      const saleProducts = await sql`SELECT * FROM products WHERE is_sale = true LIMIT 8`;

      // Format the products to ensure consistent structure
      const formattedProducts = saleProducts.map(product => ({
        id: product.id,
        name: product.name,
        description: product.description,
        price: product.price,
        discountPrice: product.discount_price,
        rating: product.rating,
        reviewCount: product.review_count,
        imageUrl: product.image_url,
        categoryId: product.category_id,
        inStock: product.in_stock,
        isNew: product.is_new,
        isPopular: product.is_popular,
        isSale: product.is_sale,
        createdAt: product.created_at
      }));

      console.log(`Returning ${formattedProducts.length} formatted sale products`);
      return res.json(formattedProducts);
    }

    // Check if ID is valid
    if (!idParam || isNaN(parseInt(idParam))) {
      console.error(`Invalid product ID: ${idParam}`);
      return res.status(400).json({
        status: 'error',
        message: `Invalid product ID: ${idParam}`
      });
    }

    const id = parseInt(idParam);

    // Use a safer query approach
    const products = await sql`SELECT * FROM products WHERE id = ${id}`;
    const product = products.length > 0 ? products[0] : null;

    if (!product) {
      console.log(`Product with ID ${id} not found`);
      return res.status(404).json({
        status: 'error',
        message: `Product with ID ${id} not found`
      });
    }

    console.log(`Found product: ${product.name}`);

    // Format the product to ensure consistent structure
    const formattedProduct = {
      id: product.id,
      name: product.name,
      description: product.description,
      price: product.price,
      discountPrice: product.discount_price,
      rating: product.rating,
      reviewCount: product.review_count,
      imageUrl: product.image_url,
      categoryId: product.category_id,
      inStock: product.in_stock,
      isNew: product.is_new,
      isPopular: product.is_popular,
      isSale: product.is_sale,
      createdAt: product.created_at
    };

    console.log(`Returning formatted product: ${formattedProduct.name} with image URL: ${formattedProduct.imageUrl}`);
    res.json(formattedProduct);
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch product',
      error: error.message
    });
  }
});

// Add API endpoint to get user
app.get('/api/user', async (req, res) => {
  try {
    // Get user ID from query parameter
    const userId = req.query.userId;

    if (!userId) {
      return res.status(401).json({
        status: 'error',
        message: 'Not authenticated'
      });
    }

    // Find user by ID
    const [user] = await sql`SELECT * FROM users WHERE id = ${parseInt(userId)}`;

    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    // Don't return the password
    delete user.password;

    // Convert snake_case to camelCase for response
    const formattedUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.full_name,
      phone: user.phone,
      address: user.address,
      isLoggedIn: user.is_logged_in,
      createdAt: user.created_at
    };

    console.log('Formatted user response:', formattedUser);

    res.json(formattedUser);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch user',
      error: error.message
    });
  }
});

// Add API endpoint for registration
app.post('/api/register', async (req, res) => {
  try {
    console.log('Registration request received with body:', req.body);
    const { username, password, email, fullName } = req.body;

    console.log(`Registration attempt for user: ${username}, email: ${email}, fullName: ${fullName}`);

    if (!username || !password || !email || !fullName) {
      console.log('Missing required fields:', {
        username: !!username,
        password: !!password,
        email: !!email,
        fullName: !!fullName
      });
      return res.status(400).json({
        status: 'error',
        message: 'Username, password, email, and full name are required',
        missing: {
          username: !username,
          password: !password,
          email: !email,
          fullName: !fullName
        }
      });
    }

    // Check if username already exists
    const existingUsersByUsername = await sql`SELECT * FROM users WHERE username = ${username}`;
    console.log(`Found ${existingUsersByUsername.length} existing users with username: ${username}`);

    if (existingUsersByUsername.length > 0) {
      console.log(`Username already exists: ${username}`);
      return res.status(409).json({
        status: 'error',
        message: 'Username already exists'
      });
    }

    // Check if email already exists
    const existingUsersByEmail = await sql`SELECT * FROM users WHERE email = ${email}`;
    console.log(`Found ${existingUsersByEmail.length} existing users with email: ${email}`);

    if (existingUsersByEmail.length > 0) {
      console.log(`Email already exists: ${email}`);
      return res.status(409).json({
        status: 'error',
        message: 'Email already exists'
      });
    }

    // Create new user
    console.log('Creating new user with data:', { username, email, fullName });
    const newUser = await sql`
      INSERT INTO users (username, password, email, full_name, is_logged_in)
      VALUES (${username}, ${password}, ${email}, ${fullName}, true)
      RETURNING *
    `;

    if (!newUser || newUser.length === 0) {
      console.error('Failed to create user - no user returned from database');
      throw new Error('Failed to create user in database');
    }

    console.log(`User registered successfully: ${username}, ID: ${newUser[0].id}`);

    // Don't return the password
    delete newUser[0].password;

    // Format the response to match client expectations
    const formattedUser = {
      id: newUser[0].id,
      username: newUser[0].username,
      email: newUser[0].email,
      fullName: newUser[0].full_name,
      address: newUser[0].address,
      phone: newUser[0].phone,
      isLoggedIn: newUser[0].is_logged_in,
      createdAt: newUser[0].created_at
    };

    console.log('Returning formatted user:', formattedUser);
    res.status(201).json({
      message: 'Registration successful',
      user: formattedUser
    });
  } catch (error) {
    console.error('Error during registration:', error);
    res.status(500).json({
      status: 'error',
      message: 'Registration failed',
      error: error.message
    });
  }
});

// Add API endpoint for login
app.post('/api/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Add validation
    if (!email || !password) {
      console.log('Missing credentials:', { email: !!email, password: !!password });
      return res.status(400).json({
        status: 'error',
        message: 'Email and password are required'
      });
    }

    console.log(`Login attempt for email: ${email}`);

    // Check if user exists
    const existingUsers = await sql`
      SELECT * FROM users
      WHERE email = ${email}
    `;

    if (existingUsers.length === 0) {
      console.log(`User not found: ${email}`);
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    const user = existingUsers[0];

    // Verify password
    if (user.password !== password) {
      console.log(`Invalid password for user: ${email}`);
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    // Update login status
    await sql`
      UPDATE users
      SET is_logged_in = true
      WHERE id = ${user.id}
    `;

    // Don't send password back
    delete user.password;

    console.log(`User logged in successfully: ${email}`);
    res.json({
      status: 'success',
      user
    });
  } catch (error) {
    console.error('Error during login:', error);
    res.status(500).json({
      status: 'error',
      message: 'Login failed',
      error: error.message
    });
  }
});

// Add API endpoint for logout
app.post('/api/logout', async (req, res) => {
  try {
    const { userId } = req.body;

    console.log(`Logout request received${userId ? ` for user ID: ${userId}` : ''}`);

    if (userId) {
      // Update user to set isLoggedIn to false
      await sql`UPDATE users SET is_logged_in = false WHERE id = ${parseInt(userId)}`;
      console.log(`User with ID ${userId} logged out successfully`);
    }

    res.json({ success: true, message: 'Logged out successfully' });
  } catch (error) {
    console.error('Error during logout:', error);
    res.status(500).json({
      status: 'error',
      message: 'Logout failed',
      error: error.message
    });
  }
});

// Add API endpoint for wishlist
app.get('/api/wishlist', async (req, res) => {
  try {
    const userId = req.query.userId;

    if (!userId) {
      return res.json([]);
    }

    console.log(`Fetching wishlist items for user ${userId}`);

    // Check user cache first
    const cachedWishlist = getUserCache(userId, 'wishlist');
    if (cachedWishlist) {
      console.log(`Returning cached wishlist for user ${userId}`);
      return res.json(cachedWishlist);
    }

    // Get wishlist items for user (removed debug queries for performance)
    const wishlistItems = await sql`
      SELECT wi.*, p.*
      FROM wishlist_items wi
      JOIN products p ON wi.product_id = p.id
      WHERE wi.user_id = ${parseInt(userId)}
    `;

    // Format the response
    const formattedItems = wishlistItems.map(item => ({
      id: item.id,
      userId: item.user_id,
      productId: item.product_id,
      createdAt: item.created_at,
      product: {
        id: item.product_id,
        name: item.name,
        description: item.description,
        price: item.price,
        discountPrice: item.discount_price,
        imageUrl: item.image_url,
        categoryId: item.category_id,
        inStock: item.in_stock,
        isNew: item.is_new,
        isPopular: item.is_popular,
        isSale: item.is_sale
      }
    }));

    // Cache the wishlist data for this user
    setUserCache(userId, 'wishlist', formattedItems, 5 * 60 * 1000); // 5 minutes cache

    res.json(formattedItems);
  } catch (error) {
    console.error('Error fetching wishlist:', error);
    res.json([]);
  }
});

// Add API endpoint to add to wishlist
app.post('/api/wishlist', async (req, res) => {
  try {
    console.log('Received wishlist add request with body:', req.body);
    const { userId, productId } = req.body;

    if (!userId || !productId) {
      console.error('Missing required fields:', { userId, productId });
      return res.status(400).json({
        status: 'error',
        message: 'User ID and Product ID are required'
      });
    }

    const userIdInt = parseInt(String(userId));
    const productIdInt = parseInt(String(productId));

    console.log(`Adding product ${productIdInt} to wishlist for user ${userIdInt}`);

    // Check if product exists
    const products = await sql`SELECT * FROM products WHERE id = ${productIdInt}`;
    console.log(`Found ${products.length} products with ID ${productIdInt}`);

    if (products.length === 0) {
      console.error(`Product with ID ${productIdInt} not found`);

      // Try to find the product by other means
      const allProducts = await sql`SELECT id FROM products LIMIT 10`;
      console.log('Available product IDs:', allProducts.map(p => p.id));

      return res.status(404).json({
        status: 'error',
        message: 'Product not found'
      });
    }

    const product = products[0];
    console.log('Found product:', product.name);

    // Debug: List all wishlist items in the database before adding
    const allWishlistItemsBefore = await sql`SELECT * FROM wishlist_items`;
    console.log(`Total wishlist items in database before adding: ${allWishlistItemsBefore.length}`);
    if (allWishlistItemsBefore.length > 0) {
      console.log('All wishlist items before adding:', allWishlistItemsBefore);
    }

    // Check if item already exists in wishlist
    const existingItems = await sql`
      SELECT * FROM wishlist_items
      WHERE user_id = ${userIdInt} AND product_id = ${productIdInt}
    `;
    console.log(`Found ${existingItems.length} existing wishlist items for user ${userIdInt} and product ${productIdInt}`);

    if (existingItems.length > 0) {
      // Item already exists
      console.log('Item already exists in wishlist:', existingItems[0]);
      res.json(existingItems[0]);
    } else {
      // Add new item
      console.log(`Adding new item to wishlist for user ${userIdInt} and product ${productIdInt}`);
      const newItems = await sql`
        INSERT INTO wishlist_items (user_id, product_id, created_at)
        VALUES (${userIdInt}, ${productIdInt}, ${new Date()})
        RETURNING *
      `;

      if (newItems.length > 0) {
        console.log('Successfully added item to wishlist:', newItems[0]);

        // Invalidate user's wishlist cache
        const cacheKey = getUserCacheKey(userIdInt, 'wishlist');
        serverCache.users.delete(cacheKey);

        res.status(201).json(newItems[0]);
      } else {
        throw new Error('Failed to insert wishlist item');
      }
    }
  } catch (error) {
    console.error('Error adding to wishlist:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to add to wishlist',
      error: error.message
    });
  }
});

// Add API endpoint to check if a product is in the wishlist
app.get('/api/wishlist/check/:productId', async (req, res) => {
  try {
    console.log(`Received request to check if product ${req.params.productId} is in wishlist`);
    const productId = parseInt(req.params.productId);
    const userId = req.query.userId;

    if (!userId || !productId) {
      console.log('Missing userId or productId in wishlist check request');
      return res.json({ isInWishlist: false });
    }

    console.log(`Checking if product ${productId} is in wishlist for user ${userId}`);

    // Check if the product is in the user's wishlist
    const wishlistItems = await sql`
      SELECT * FROM wishlist_items
      WHERE user_id = ${parseInt(userId)} AND product_id = ${productId}
    `;

    const isInWishlist = wishlistItems.length > 0;
    console.log(`Product ${productId} is ${isInWishlist ? '' : 'not '}in wishlist for user ${userId}`);

    res.json({ isInWishlist });
  } catch (error) {
    console.error('Error checking wishlist status:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to check wishlist status',
      error: error.message
    });
  }
});

// Add API endpoint to remove from wishlist by item ID
app.delete('/api/wishlist/:id', async (req, res) => {
  try {
    const itemId = parseInt(req.params.id);
    const userId = req.query.userId;

    console.log(`Received DELETE request for wishlist item ID: ${itemId}, userId: ${userId}`);

    if (!userId) {
      console.log('Missing userId in wishlist delete request');
      return res.status(400).json({
        status: 'error',
        message: 'User ID is required'
      });
    }

    const userIdInt = parseInt(String(userId));

    // First try to find by wishlist item ID
    let existingItems = await sql`SELECT * FROM wishlist_items WHERE id = ${itemId}`;
    console.log(`Found ${existingItems.length} wishlist items with ID ${itemId}`);

    // If not found by ID, try to find by product ID
    if (existingItems.length === 0) {
      console.log(`Trying to find wishlist item by product ID ${itemId} for user ${userIdInt}`);
      existingItems = await sql`SELECT * FROM wishlist_items WHERE product_id = ${itemId} AND user_id = ${userIdInt}`;
      console.log(`Found ${existingItems.length} wishlist items with product ID ${itemId} for user ${userIdInt}`);
    }

    if (existingItems.length === 0) {
      console.log(`No wishlist item found for ID ${itemId} or product ID ${itemId} for user ${userIdInt}`);
      return res.status(404).json({
        status: 'error',
        message: 'Wishlist item not found'
      });
    }

    // Log the item we're about to delete
    console.log(`Deleting wishlist item:`, existingItems[0]);

    // Delete wishlist item by the found ID
    const foundItemId = existingItems[0].id;
    const result = await sql`DELETE FROM wishlist_items WHERE id = ${foundItemId} RETURNING *`;
    console.log(`Deletion result:`, result);

    // Check if any rows were affected
    if (result.length === 0) {
      console.log(`No rows were affected when deleting wishlist item ${foundItemId}`);
      return res.status(404).json({
        status: 'error',
        message: 'Wishlist item not found or could not be deleted'
      });
    }

    console.log(`Successfully deleted wishlist item ${foundItemId}`);

    // Invalidate user's wishlist cache
    if (userId) {
      const cacheKey = getUserCacheKey(parseInt(userId), 'wishlist');
      serverCache.users.delete(cacheKey);
    }

    res.status(200).json({
      status: 'success',
      message: 'Wishlist item deleted successfully',
      deletedItem: result[0]
    });
  } catch (error) {
    console.error('Error removing from wishlist:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to remove from wishlist',
      error: error.message
    });
  }
});

// Add API endpoint for cart
app.get('/api/cart', async (req, res) => {
  try {
    const userId = req.query.userId;

    if (!userId) {
      return res.json([]);
    }

    // Check user cache first
    const cachedCart = getUserCache(userId, 'cart');
    if (cachedCart) {
      console.log(`Returning cached cart for user ${userId}`);
      return res.json(cachedCart);
    }

    // Get cart items for user
    const cartItems = await sql`
      SELECT ci.*, p.*
      FROM cart_items ci
      JOIN products p ON ci.product_id = p.id
      WHERE ci.user_id = ${parseInt(userId)}
    `;

    // Format the response
    const formattedItems = cartItems.map(item => ({
      id: item.id,
      userId: item.user_id,
      productId: item.product_id,
      quantity: item.quantity,
      createdAt: item.created_at,
      product: {
        id: item.product_id,
        name: item.name,
        description: item.description,
        price: item.price,
        discountPrice: item.discount_price,
        imageUrl: item.image_url,
        categoryId: item.category_id,
        inStock: item.in_stock,
        isNew: item.is_new,
        isPopular: item.is_popular,
        isSale: item.is_sale
      }
    }));

    // Cache the cart data for this user
    setUserCache(userId, 'cart', formattedItems, 3 * 60 * 1000); // 3 minutes cache

    res.json(formattedItems);
  } catch (error) {
    console.error('Error fetching cart:', error);
    res.json([]);
  }
});

// Add API endpoint to add to cart
app.post('/api/cart', async (req, res) => {
  try {
    console.log('Received cart add request with body:', req.body);
    let { userId, productId, quantity } = req.body;

    // Try to get userId from query parameters if not in body
    if (!userId && req.query.userId) {
      userId = req.query.userId;
      console.log(`Using userId from query parameters: ${userId}`);
    }

    // Try to get userId from localStorage if not in body or query
    if (!userId && req.headers.cookie) {
      const cookies = req.headers.cookie.split(';').map(cookie => cookie.trim());
      const userIdCookie = cookies.find(cookie => cookie.startsWith('userId='));
      if (userIdCookie) {
        userId = userIdCookie.split('=')[1];
        console.log(`Using userId from cookies: ${userId}`);
      }
    }

    if (!userId || !productId) {
      console.error('Missing required fields:', { userId, productId });
      return res.status(400).json({
        status: 'error',
        message: 'User ID and Product ID are required'
      });
    }

    const userIdInt = parseInt(String(userId));
    const productIdInt = parseInt(String(productId));
    const quantityInt = quantity ? parseInt(String(quantity)) : 1;

    console.log(`Adding product ${productIdInt} to cart for user ${userIdInt} with quantity ${quantityInt}`);

    // Check if product exists
    const products = await sql`SELECT * FROM products WHERE id = ${productIdInt}`;
    console.log(`Found ${products.length} products with ID ${productIdInt}`);

    if (products.length === 0) {
      console.error(`Product with ID ${productIdInt} not found`);

      // Try to find the product by other means
      const allProducts = await sql`SELECT id FROM products LIMIT 10`;
      console.log('Available product IDs:', allProducts.map(p => p.id));

      return res.status(404).json({
        status: 'error',
        message: 'Product not found'
      });
    }

    const product = products[0];
    console.log('Found product:', product.name);

    // Check if item already exists in cart
    const existingItems = await sql`
      SELECT * FROM cart_items
      WHERE user_id = ${userIdInt} AND product_id = ${productIdInt}
    `;
    console.log(`Found ${existingItems.length} existing cart items`);

    if (existingItems.length > 0) {
      // Update quantity
      console.log('Updating existing cart item');
      const newQuantity = (existingItems[0].quantity || 0) + quantityInt;
      console.log(`Updating quantity from ${existingItems[0].quantity} to ${newQuantity}`);

      const updatedItems = await sql`
        UPDATE cart_items
        SET quantity = ${newQuantity}
        WHERE id = ${existingItems[0].id}
        RETURNING *
      `;

      if (updatedItems.length > 0) {
        console.log('Successfully updated cart item:', updatedItems[0]);
        res.json(updatedItems[0]);
      } else {
        throw new Error('Failed to update cart item');
      }
    } else {
      // Add new item
      console.log('Adding new item to cart');
      const newItems = await sql`
        INSERT INTO cart_items (user_id, product_id, quantity, created_at)
        VALUES (${userIdInt}, ${productIdInt}, ${quantityInt}, ${new Date()})
        RETURNING *
      `;

      if (newItems.length > 0) {
        console.log('Successfully added item to cart:', newItems[0]);

        // Invalidate user's cart cache
        const cacheKey = getUserCacheKey(userIdInt, 'cart');
        serverCache.users.delete(cacheKey);

        res.status(201).json(newItems[0]);
      } else {
        throw new Error('Failed to insert cart item');
      }
    }
  } catch (error) {
    console.error('Error adding to cart:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to add to cart',
      error: error.message
    });
  }
});

// Add API endpoint to update cart item
app.put('/api/cart/:id', async (req, res) => {
  try {
    const itemId = parseInt(req.params.id);
    const { quantity } = req.body;

    // Update cart item
    const [updatedItem] = await sql`
      UPDATE cart_items
      SET quantity = ${quantity}
      WHERE id = ${itemId}
      RETURNING *
    `;

    if (!updatedItem) {
      return res.status(404).json({
        status: 'error',
        message: 'Cart item not found'
      });
    }

    res.json(updatedItem);
  } catch (error) {
    console.error('Error updating cart item:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update cart item',
      error: error.message
    });
  }
});

// Add API endpoint to remove from cart
app.delete('/api/cart/:id', async (req, res) => {
  try {
    const itemId = parseInt(req.params.id);
    const userId = req.query.userId;

    console.log(`Received DELETE request for cart item ID: ${itemId}, userId: ${userId}`);

    if (!userId) {
      console.log('Missing userId in cart delete request');
      return res.status(400).json({
        status: 'error',
        message: 'User ID is required'
      });
    }

    const userIdInt = parseInt(String(userId));

    // First try to find by cart item ID
    let existingItems = await sql`SELECT * FROM cart_items WHERE id = ${itemId}`;
    console.log(`Found ${existingItems.length} cart items with ID ${itemId}`);

    // If not found by ID, try to find by product ID
    if (existingItems.length === 0) {
      console.log(`Trying to find cart item by product ID ${itemId} for user ${userIdInt}`);
      existingItems = await sql`SELECT * FROM cart_items WHERE product_id = ${itemId} AND user_id = ${userIdInt}`;
      console.log(`Found ${existingItems.length} cart items with product ID ${itemId} for user ${userIdInt}`);
    }

    if (existingItems.length === 0) {
      console.log(`No cart item found for ID ${itemId} or product ID ${itemId} for user ${userIdInt}`);
      return res.status(404).json({
        status: 'error',
        message: 'Cart item not found'
      });
    }

    // Log the item we're about to delete
    console.log(`Deleting cart item:`, existingItems[0]);

    // Delete cart item by the found ID
    const foundItemId = existingItems[0].id;
    const result = await sql`DELETE FROM cart_items WHERE id = ${foundItemId} RETURNING *`;
    console.log(`Deletion result:`, result);

    // Check if any rows were affected
    if (result.length === 0) {
      console.log(`No rows were affected when deleting cart item ${foundItemId}`);
      return res.status(404).json({
        status: 'error',
        message: 'Cart item not found or could not be deleted'
      });
    }

    console.log(`Successfully deleted cart item ${foundItemId}`);

    // Invalidate user's cart cache
    if (userId) {
      const cacheKey = getUserCacheKey(parseInt(userId), 'cart');
      serverCache.users.delete(cacheKey);
    }

    res.status(200).json({
      status: 'success',
      message: 'Cart item deleted successfully',
      deletedItem: result[0]
    });
  } catch (error) {
    console.error('Error removing from cart:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to remove from cart',
      error: error.message
    });
  }
});

// Add API endpoint to clear cart
app.delete('/api/cart', async (req, res) => {
  try {
    const userId = req.query.userId;

    if (!userId) {
      return res.status(400).json({
        status: 'error',
        message: 'User ID is required'
      });
    }

    // Delete all cart items for user
    await sql`DELETE FROM cart_items WHERE user_id = ${parseInt(userId)}`;

    res.status(204).end();
  } catch (error) {
    console.error('Error clearing cart:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to clear cart',
      error: error.message
    });
  }
});

// Add API endpoint to update user profile
app.put('/api/profile', async (req, res) => {
  try {
    console.log('Received profile update request with body:', req.body);
    const { fullName, email, phone, address } = req.body;
    const userId = req.query.userId;

    if (!userId) {
      console.error('Missing userId in profile update request');
      return res.status(400).json({
        status: 'error',
        message: 'User ID is required'
      });
    }

    console.log(`Updating profile for user ${userId}`);

    // Check if user exists
    const [user] = await sql`SELECT * FROM users WHERE id = ${parseInt(userId)}`;

    if (!user) {
      console.error(`User with ID ${userId} not found`);
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    // Update user profile
    const [updatedUser] = await sql`
      UPDATE users
      SET
        full_name = ${fullName || user.full_name || ''},
        email = ${email || user.email || ''},
        phone = ${phone || user.phone || ''},
        address = ${address || user.address || ''}
      WHERE id = ${parseInt(userId)}
      RETURNING *
    `;

    console.log('User profile updated successfully:', updatedUser);

    // Don't return the password
    delete updatedUser.password;

    // Convert snake_case to camelCase for response
    const formattedUser = {
      id: updatedUser.id,
      username: updatedUser.username,
      email: updatedUser.email,
      fullName: updatedUser.full_name,
      phone: updatedUser.phone,
      address: updatedUser.address,
      isLoggedIn: updatedUser.is_logged_in,
      createdAt: updatedUser.created_at
    };

    res.json(formattedUser);
  } catch (error) {
    console.error('Error updating profile:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update profile',
      error: error.message
    });
  }
});

// Add API endpoint to get user orders
app.get('/api/orders', async (req, res) => {
  try {
    console.log('Received request for orders with query params:', req.query);
    console.log('Request headers:', req.headers);

    const userId = req.query.userId;

    if (!userId) {
      console.error('No userId provided in request');
      return res.status(400).json({
        status: 'error',
        message: 'User ID is required'
      });
    }

    console.log(`Fetching orders for user ${userId}`);

    // Check if the orders table exists
    try {
      const tableCheck = await sql`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'orders'
        );
      `;
      console.log('Orders table exists:', tableCheck[0].exists);

      if (!tableCheck[0].exists) {
        console.error('Orders table does not exist');
        return res.status(500).json({
          status: 'error',
          message: 'Orders table does not exist'
        });
      }
    } catch (tableError) {
      console.error('Error checking if orders table exists:', tableError);
    }

    // Get orders for user
    const orders = await sql`
      SELECT * FROM orders
      WHERE user_id = ${parseInt(userId)}
      ORDER BY created_at DESC
    `;

    console.log(`Found ${orders.length} orders for user ${userId}`);

    if (orders.length === 0) {
      // Check if there are any orders in the table at all
      const allOrders = await sql`SELECT COUNT(*) FROM orders`;
      console.log(`Total orders in database: ${allOrders[0].count}`);

      // Check if the user exists
      const userCheck = await sql`SELECT * FROM users WHERE id = ${parseInt(userId)}`;
      console.log(`User exists: ${userCheck.length > 0}`);
    } else {
      // Log the first order for debugging
      console.log('First order:', orders[0]);
    }

    // Convert snake_case to camelCase for order properties
    const formattedOrders = orders.map(order => ({
      id: order.id,
      userId: order.user_id,
      status: order.status,
      total: order.total,
      address: order.address,
      paymentMethod: order.payment_method,
      createdAt: order.created_at
    }));

    console.log('Formatted orders response:', formattedOrders);

    res.json(formattedOrders);
  } catch (error) {
    console.error('Error fetching orders:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch orders',
      error: error.message
    });
  }
});

// Add API endpoint to get order details
app.get('/api/orders/:id', async (req, res) => {
  try {
    const orderId = parseInt(req.params.id);
    const userId = req.query.userId;

    if (!userId) {
      return res.status(400).json({
        status: 'error',
        message: 'User ID is required'
      });
    }

    console.log(`Fetching order ${orderId} for user ${userId}`);

    // Get order
    const [order] = await sql`
      SELECT * FROM orders
      WHERE id = ${orderId} AND user_id = ${parseInt(userId)}
    `;

    if (!order) {
      return res.status(404).json({
        status: 'error',
        message: 'Order not found'
      });
    }

    // Get order items with category information
    const orderItems = await sql`
      SELECT oi.*, p.*, c.id as category_id, c.name as category_name
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE oi.order_id = ${orderId}
    `;

    console.log('Order items with category data:', orderItems);

    // Format the response
    const formattedItems = orderItems.map(item => ({
      id: item.id,
      orderId: item.order_id,
      productId: item.product_id,
      quantity: item.quantity,
      price: item.price,
      product: {
        id: item.product_id,
        name: item.name,
        description: item.description,
        price: item.price,
        discountPrice: item.discount_price,
        imageUrl: item.image_url,
        categoryId: item.category_id,
        inStock: item.in_stock,
        isNew: item.is_new,
        isPopular: item.is_popular,
        isSale: item.is_sale,
        category: {
          id: item.category_id,
          name: item.category_name || 'Uncategorized'
        }
      }
    }));

    // Convert snake_case to camelCase for order properties
    const formattedOrder = {
      id: order.id,
      userId: order.user_id,
      status: order.status,
      total: order.total,
      address: order.address,
      paymentMethod: order.payment_method,
      createdAt: order.created_at,
      items: formattedItems
    };

    console.log('Formatted order response:', formattedOrder);

    res.json(formattedOrder);
  } catch (error) {
    console.error('Error fetching order details:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch order details',
      error: error.message
    });
  }
});

// Add API endpoint to create an order
app.post('/api/orders', async (req, res) => {
  try {
    console.log('Received order creation request with body:', req.body);
    console.log('Request headers:', req.headers);

    const { userId, address, paymentMethod, total, items } = req.body;

    if (!userId || !address || !paymentMethod || !items || !Array.isArray(items)) {
      console.error('Missing required fields:', { userId, address, paymentMethod, items });
      return res.status(400).json({
        status: 'error',
        message: 'User ID, address, payment method, and items are required'
      });
    }

    const userIdInt = parseInt(String(userId));

    console.log(`Creating order for user ${userIdInt}`);

    // Check if the user exists
    const userCheck = await sql`SELECT * FROM users WHERE id = ${userIdInt}`;
    console.log(`User exists: ${userCheck.length > 0}`);

    if (userCheck.length === 0) {
      console.error(`User with ID ${userIdInt} does not exist`);
      return res.status(400).json({
        status: 'error',
        message: 'User does not exist'
      });
    }

    // Check if the orders table exists
    try {
      const tableCheck = await sql`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'orders'
        );
      `;
      console.log('Orders table exists:', tableCheck[0].exists);

      if (!tableCheck[0].exists) {
        console.error('Orders table does not exist');
        return res.status(500).json({
          status: 'error',
          message: 'Orders table does not exist'
        });
      }
    } catch (tableError) {
      console.error('Error checking if orders table exists:', tableError);
    }

    // Create order
    const [order] = await sql`
      INSERT INTO orders (user_id, status, total, address, payment_method, created_at)
      VALUES (${userIdInt}, 'pending', ${total}, ${address}, ${paymentMethod}, ${new Date()})
      RETURNING *
    `;

    console.log('Order created:', order);

    // Create order items
    const orderItems = [];
    for (const item of items) {
      console.log(`Adding item to order: ${JSON.stringify(item)}`);

      const [orderItem] = await sql`
        INSERT INTO order_items (order_id, product_id, quantity, price, created_at)
        VALUES (${order.id}, ${item.productId}, ${item.quantity}, ${item.price}, ${new Date()})
        RETURNING *
      `;

      orderItems.push(orderItem);
    }

    console.log(`Added ${orderItems.length} items to order ${order.id}`);

    // Clear user's cart
    await sql`DELETE FROM cart_items WHERE user_id = ${userIdInt}`;
    console.log(`Cleared cart for user ${userIdInt}`);

    // Verify the order was created
    const orderCheck = await sql`SELECT * FROM orders WHERE id = ${order.id}`;
    console.log(`Order exists in database: ${orderCheck.length > 0}`);

    if (orderCheck.length > 0) {
      console.log('Order in database:', orderCheck[0]);
    }

    res.status(201).json({
      ...order,
      items: orderItems
    });
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to create order',
      error: error.message
    });
  }
});

// Add a special route for image placeholders
app.get('/placeholder-category.jpg', (req, res) => {
  console.log('Serving placeholder category image');

  // Try to find the image in various locations
  const possibleImagePaths = [
    path.join(process.cwd(), 'client/public/placeholder-category.jpg'),
    path.join(process.cwd(), 'client/dist/placeholder-category.jpg'),
    path.join(process.cwd(), 'public/placeholder-category.jpg'),
    path.join(process.cwd(), 'dist/public/placeholder-category.jpg')
  ];

  // Find the first existing image path
  for (const imagePath of possibleImagePaths) {
    if (fs.existsSync(imagePath)) {
      console.log(`Found placeholder image at ${imagePath}`);
      return res.sendFile(imagePath);
    }
  }

  // If no image is found, redirect to a placeholder image URL
  console.log('No placeholder image found, redirecting to external placeholder');
  return res.redirect('https://via.placeholder.com/300x200/CCCCCC/666666?text=Category');
});

// Add a special route for product images
app.get('/images/:imageName', (req, res) => {
  const imageName = req.params.imageName;
  console.log(`Serving image: ${imageName}`);

  // Try to find the image in various locations
  const possibleImagePaths = [
    path.join(process.cwd(), `client/public/images/${imageName}`),
    path.join(process.cwd(), `client/dist/images/${imageName}`),
    path.join(process.cwd(), `public/images/${imageName}`),
    path.join(process.cwd(), `dist/public/images/${imageName}`)
  ];

  // Find the first existing image path
  for (const imagePath of possibleImagePaths) {
    if (fs.existsSync(imagePath)) {
      console.log(`Found image at ${imagePath}`);
      return res.sendFile(imagePath);
    }
  }

  // If no image is found, redirect to a placeholder or external image
  console.log('Image not found, redirecting to placeholder');

  // Use a simple default placeholder for all images
  return res.redirect('https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg');
});

// Add a catch-all route to serve the index.html file for client-side routing
app.get('*', (req, res) => {
  // Skip API routes
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({
      status: 'error',
      message: `API endpoint not found: ${req.path}`
    });
  }

  // Check if the path looks like an image request
  if (req.path.match(/\.(jpg|jpeg|png|gif|svg|webp)$/i)) {
    console.log(`Image request detected: ${req.path}`);

    // Try to find the image in various locations
    const fileName = path.basename(req.path);
    const possibleImagePaths = [
      path.join(process.cwd(), `client/public${req.path}`),
      path.join(process.cwd(), `client/dist${req.path}`),
      path.join(process.cwd(), `public${req.path}`),
      path.join(process.cwd(), `dist/public${req.path}`)
    ];

    // Find the first existing image path
    for (const imagePath of possibleImagePaths) {
      if (fs.existsSync(imagePath)) {
        console.log(`Found image at ${imagePath}`);
        return res.sendFile(imagePath);
      }
    }

    // If no image is found, redirect to a placeholder
    console.log('Image not found, redirecting to external placeholder');
    return res.redirect('https://images.pexels.com/photos/4195325/pexels-photo-4195325.jpeg');
  }

  // If we found an index.html file, serve it
  if (indexHtmlPath) {
    console.log(`Serving index.html for path: ${req.path}`);
    res.sendFile(indexHtmlPath);
  } else {
    // Otherwise, serve a simple HTML page
    res.send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Welcome</title>
          <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            h1 { color: #333; }
            .info { background: #f5f5f5; padding: 15px; border-radius: 5px; }
            .warning { background: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 20px; }
          </style>
        </head>
        <body>
          <h1>Welcome to the E-commerce App</h1>
          <div class="info">
            <p>The server is running correctly, but no frontend files were found.</p>
            <p>Environment: ${process.env.NODE_ENV || 'development'}</p>
            <p>Database: ${process.env.DATABASE_URL ? 'Configured' : 'Not configured'}</p>
          </div>
          <div class="warning">
            <p><strong>Note:</strong> To see the frontend, you need to build the project first:</p>
            <pre>npm run build</pre>
            <p>Or you can access the API directly:</p>
            <ul>
              <li><a href="/api/health">/api/health</a> - Check server health</li>
              <li><a href="/api/categories">/api/categories</a> - Get all categories</li>
              <li><a href="/api/products">/api/products</a> - Get all products</li>
            </ul>
          </div>
        </body>
      </html>
    `);
  }
});

// Initialize database tables
async function initializeDatabase() {
  try {
    console.log('Initializing database tables...');

    // Create users table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(255) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(255),
        address TEXT,
        phone VARCHAR(50),
        is_logged_in BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create categories table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        image_url TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create products table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10, 2) NOT NULL,
        discount_price DECIMAL(10, 2),
        rating DECIMAL(3, 1) DEFAULT 0,
        review_count INTEGER DEFAULT 0,
        image_url TEXT,
        category_id INTEGER REFERENCES categories(id),
        in_stock BOOLEAN DEFAULT true,
        is_new BOOLEAN DEFAULT false,
        is_popular BOOLEAN DEFAULT false,
        is_sale BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create wishlist_items table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS wishlist_items (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, product_id)
      )
    `;

    // Create cart_items table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS cart_items (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
        quantity INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, product_id)
      )
    `;

    // Create orders table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        status VARCHAR(50) DEFAULT 'pending',
        total DECIMAL(10, 2) NOT NULL,
        address TEXT NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create order_items table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS order_items (
        id SERIAL PRIMARY KEY,
        order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
        product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
        quantity INTEGER NOT NULL,
        price DECIMAL(10, 2) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Demo user has been removed

    console.log('Database initialization complete');
  } catch (error) {
    console.error('Error initializing database:', error);
  }
}

// Initialize the database before starting the server
initializeDatabase().then(() => {
  // Start the server
  const port = process.env.PORT || 3001; // Use environment variable or default to 3001
  const host = process.env.HOST || '0.0.0.0';

  console.log(`Starting server on port ${port}`);

  app.listen(port, host, () => {
    console.log(`Server running at http://localhost:${port}`);
    console.log(`You can access the test endpoint at http://localhost:${port}/test`);
    console.log(`You can access the test HTML endpoint at http://localhost:${port}/test-html`);
    console.log(`You can access the API health endpoint at http://localhost:${port}/api/health`);
    console.log(`You can access the API categories endpoint at http://localhost:${port}/api/categories`);
    console.log(`You can access the API products endpoint at http://localhost:${port}/api/products`);
    console.log(`You can access the API orders endpoint at http://localhost:${port}/api/orders?userId=YOUR_USER_ID`);
  });
});

